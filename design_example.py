"""
1.5GHz微带线低通滤波器设计示例
完整的设计流程演示
"""

import sys
import os
import numpy as np
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.main import FilterDesigner
from config import DESIGN_SPECS, SUBSTRATE_PARAMS

def print_design_specifications():
    """打印设计规格"""
    print("设计规格:")
    print(f"  通带频率: {DESIGN_SPECS['passband_start']/1e9:.1f} - {DESIGN_SPECS['passband_end']/1e9:.1f} GHz")
    print(f"  阻带频率: {DESIGN_SPECS['stopband_start']/1e9:.1f} - {DESIGN_SPECS['stopband_end']/1e9:.1f} GHz")
    print(f"  通带插入损耗: < {DESIGN_SPECS['passband_insertion_loss_max']:.1f} dB")
    print(f"  通带回波损耗: > {DESIGN_SPECS['passband_return_loss_min']:.1f} dB")
    print(f"  阻带插入损耗: > {DESIGN_SPECS['stopband_insertion_loss_min']:.1f} dB")
    print(f"  系统阻抗: {DESIGN_SPECS['system_impedance']:.1f} Ω")
    
    print("\n基板参数:")
    print(f"  材料: {SUBSTRATE_PARAMS['material']}")
    print(f"  厚度: {SUBSTRATE_PARAMS['thickness']*1000:.3f} mm")
    print(f"  介电常数: {SUBSTRATE_PARAMS['dielectric_constant']}")
    print(f"  损耗角正切: {SUBSTRATE_PARAMS['loss_tangent']}")

def save_design_report(complete_result: dict, filename: str):
    """保存设计报告"""
    report = {
        'design_summary': {
            'timestamp': complete_result['timestamp'],
            'filter_order': int(complete_result['design_result']['filter_order']),
            'cutoff_frequency_ghz': float(complete_result['design_result']['cutoff_frequency'] / 1e9),
            'total_length_mm': float(complete_result['design_result']['total_length'] * 1000),
            'max_width_mm': float(complete_result['design_result']['max_width'] * 1000),
            'size_constraints_met': bool(complete_result['design_result']['size_constraints']['constraints_met'])
        },
        'performance_results': {
            'passband_max_insertion_loss_db': float(complete_result['simulation_result']['performance_analysis']['passband_max_insertion_loss']),
            'passband_min_return_loss_db': float(complete_result['simulation_result']['performance_analysis']['passband_min_return_loss']),
            'stopband_min_insertion_loss_db': float(complete_result['simulation_result']['performance_analysis']['stopband_min_insertion_loss']),
            'all_specs_met': bool(complete_result['simulation_result']['performance_analysis']['all_specs_met'])
        },
        'microstrip_sections': []
    }
    
    # 添加微带线段信息
    for i, section in enumerate(complete_result['design_result']['microstrip_sections']):
        if 'width' in section and 'length' in section:
            report['microstrip_sections'].append({
                'section_number': i + 1,
                'type': section.get('type', 'unknown'),
                'description': section.get('description', ''),
                'width_mm': float(section['width'] * 1000),
                'length_mm': float(section['length'] * 1000),
                'impedance_ohm': float(section.get('impedance', 0)) if isinstance(section.get('impedance'), (int, float)) else 'N/A'
            })
    
    # 保存JSON报告
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"设计报告已保存到: {filename}")

def print_performance_summary(simulation_result: dict):
    """打印性能总结"""
    perf = simulation_result['performance_analysis']
    
    print("\n性能分析结果:")
    print(f"  通带最大插入损耗: {perf['passband_max_insertion_loss']:.3f} dB (规格: < {DESIGN_SPECS['passband_insertion_loss_max']:.1f} dB)")
    print(f"  通带最小回波损耗: {perf['passband_min_return_loss']:.3f} dB (规格: > {DESIGN_SPECS['passband_return_loss_min']:.1f} dB)")
    print(f"  阻带最小插入损耗: {perf['stopband_min_insertion_loss']:.3f} dB (规格: > {DESIGN_SPECS['stopband_insertion_loss_min']:.1f} dB)")
    print(f"  通带纹波: {perf['passband_ripple']:.3f} dB")
    
    print("\n规格符合性:")
    print(f"  通带插入损耗: {'✓' if perf['passband_insertion_loss_spec_met'] else '✗'}")
    print(f"  通带回波损耗: {'✓' if perf['passband_return_loss_spec_met'] else '✗'}")
    print(f"  阻带插入损耗: {'✓' if perf['stopband_insertion_loss_spec_met'] else '✗'}")
    print(f"  总体符合性: {'✓ 通过' if perf['all_specs_met'] else '✗ 不通过'}")

def print_filter_structure(design_result: dict):
    """打印滤波器结构"""
    print("\n滤波器物理结构:")
    print(f"  总长度: {design_result['total_length']*1000:.2f} mm")
    print(f"  最大宽度: {design_result['max_width']*1000:.2f} mm")
    print(f"  段数: {len(design_result['microstrip_sections'])}")
    
    print("\n各段详细参数:")
    for i, section in enumerate(design_result['microstrip_sections']):
        if 'width' in section and 'length' in section:
            print(f"  段{i+1}: {section.get('description', 'N/A')}")
            print(f"    宽度: {section['width']*1000:.3f} mm")
            print(f"    长度: {section['length']*1000:.3f} mm")
            if 'impedance' in section:
                print(f"    阻抗: {section['impedance']:.1f} Ω")

def main():
    """主函数"""
    print("1.5GHz微带线低通滤波器设计示例")
    print("="*60)
    
    # 打印设计规格
    print_design_specifications()
    
    print("\n" + "="*60)
    print("开始设计流程...")
    
    try:
        # 创建设计器
        designer = FilterDesigner()
        
        # 执行完整设计流程
        complete_result = designer.design_and_simulate(
            target_order=5,             # 固定5阶以加快速度
            enable_optimization=True,   # 启用优化
            max_generations=100          # 减少优化代数以节省时间
        )
        
        print("\n" + "="*60)
        print("设计完成! 结果分析:")
        
        # 打印滤波器结构
        print_filter_structure(complete_result['design_result'])
        
        # 打印性能总结
        print_performance_summary(complete_result['simulation_result'])
        
        # 保存设计报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"results/reports/filter_design_report_{timestamp}.json"
        save_design_report(complete_result, report_filename)
        
        # 优化结果
        if complete_result['optimization_result'] is not None:
            opt_result = complete_result['optimization_result']
            print(f"\n优化结果:")
            print(f"  最佳适应度: {opt_result['best_fitness']:.6f}")
            print(f"  优化成功: {'是' if opt_result['optimization_success'] else '否'}")
            print(f"  收敛代数: {opt_result['convergence_generation']}")
        
        print(f"\n设计总结:")
        design_success = complete_result['simulation_result']['performance_analysis']['all_specs_met']
        print(f"  设计成功: {'是' if design_success else '否'}")
        
        if design_success:
            print("🎉 恭喜! 滤波器设计满足所有性能指标!")
        else:
            print("⚠️  滤波器设计未完全满足性能指标，建议进一步优化。")
        
        print(f"\n所有结果文件已保存到 results/ 目录")
        print("包括:")
        print("  - S参数图表")
        print("  - 滤波器布局图")
        print("  - 性能对比图")
        print("  - 优化历史图 (如果启用优化)")
        print("  - 设计报告 (JSON格式)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 设计过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*60)
    if success:
        print("设计示例执行完成!")
    else:
        print("设计示例执行失败!")
    
    sys.exit(0 if success else 1)
